/* Reset and Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    color: #e0e0e0;
    line-height: 1.6;
    background-color: #1a1a2e;
    font-size: 16px;
    font-weight: 400;
    letter-spacing: 0.3px;
}

a {
    text-decoration: none;
    color: inherit;
    transition: color 0.3s ease;
}

ul {
    list-style: none;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

h2 {
    text-align: center;
    margin-bottom: 50px;
    font-size: 2.5rem;
    color: #c7b5ff;
    position: relative;
    font-weight: 700;
    letter-spacing: 1px;
}

h2::after {
    content: '';
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #6c63ff, #00d4ff);
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

/* Navigation Styles */
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 40px;
    background-color: rgba(13, 13, 23, 0.95);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.navbar.scrolled {
    background-color: #24243e;
    box-shadow: 0 5px 20px rgba(108, 99, 255, 0.4);
}

.logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: #7c6eff;
    transition: transform 0.3s ease, color 0.3s ease;
    position: relative;
    display: inline-flex;
    align-items: center;
    letter-spacing: 1px;
}

.logo:hover {
    transform: scale(1.05);
    color: #00ccff;
}

.logo::before {
    content: '';
    position: absolute;
    left: -10px;
    width: 5px;
    height: 25px;
    background: linear-gradient(90deg, #7c6eff, #00ccff);
    border-radius: 2px;
}

.nav-links {
    display: flex;
    gap: 30px;
}

.nav-links li {
    position: relative;
}

.nav-links li a {
    font-size: 1.1rem;
    color: #f0f0f5;
    font-weight: 500;
    transition: color 0.3s ease, transform 0.3s ease;
    position: relative;
    padding: 8px 12px;
    border-radius: 5px;
    letter-spacing: 0.5px;
}

.nav-links li a::before {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #7c6eff, #00ccff);
    transition: width 0.3s ease;
}

.nav-links li a:hover {
    color: #7c6eff;
    transform: translateY(-2px);
    display: inline-block;
}

.nav-links li a:hover::before {
    width: 100%;
}

.nav-extras {
    display: flex;
    align-items: center;
    gap: 20px;
}

.theme-toggle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(108, 99, 255, 0.2);
    color: #f0f0f5;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    transition: background 0.3s ease, transform 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(108, 99, 255, 0.3);
    position: relative;
}

.theme-toggle:hover {
    background: linear-gradient(90deg, #7c6eff, #00ccff);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 0 10px rgba(108, 99, 255, 0.5);
}

.theme-dropdown {
    position: absolute;
    top: 50px;
    right: 0;
    background-color: rgba(36, 36, 62, 0.95);
    border: 1px solid rgba(108, 99, 255, 0.3);
    border-radius: 8px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    min-width: 150px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
    z-index: 1001;
}

.theme-toggle:hover .theme-dropdown,
.theme-dropdown:hover {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.theme-dropdown a {
    display: block;
    padding: 12px 20px;
    color: #e0e0e0;
    font-size: 0.9rem;
    transition: background 0.3s ease, color 0.3s ease;
    border-bottom: 1px solid rgba(108, 99, 255, 0.2);
    font-weight: 500;
    letter-spacing: 0.5px;
}

.theme-dropdown a:last-child {
    border-bottom: none;
}

.theme-dropdown a:hover {
    background-color: rgba(108, 99, 255, 0.2);
    color: #6c63ff;
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(108, 99, 255, 0.2);
    color: #f0f0f5;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    transition: background 0.3s ease, transform 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(108, 99, 255, 0.3);
    position: relative;
}

.profile-icon:hover {
    background: linear-gradient(90deg, #7c6eff, #00ccff);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 0 10px rgba(108, 99, 255, 0.5);
}

.profile-dropdown {
    position: absolute;
    top: 50px;
    right: 0;
    background-color: rgba(36, 36, 62, 0.95);
    border: 1px solid rgba(108, 99, 255, 0.3);
    border-radius: 8px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    min-width: 150px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
    z-index: 1001;
}

.profile-icon:hover .profile-dropdown,
.profile-dropdown:hover {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.profile-dropdown a {
    display: block;
    padding: 12px 20px;
    color: #e0e0e0;
    font-size: 0.9rem;
    transition: background 0.3s ease, color 0.3s ease;
    border-bottom: 1px solid rgba(108, 99, 255, 0.2);
    font-weight: 500;
    letter-spacing: 0.5px;
}

.profile-dropdown a:last-child {
    border-bottom: none;
}

.profile-dropdown a:hover {
    background-color: rgba(108, 99, 255, 0.2);
    color: #6c63ff;
}

.hamburger {
    display: none;
    flex-direction: column;
    gap: 5px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.hamburger:hover {
    transform: scale(1.1);
}

.hamburger span {
    width: 25px;
    height: 3px;
    background-color: #e0e0e0;
    transition: all 0.3s ease, background-color 0.3s ease;
    border-radius: 2px;
}

.hamburger:hover span {
    background-color: #6c63ff;
}

.hamburger.active span:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
    background-color: #6c63ff;
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
    background-color: #6c63ff;
}

/* Hero Section */
.hero {
    height: 100vh;
    background: url('https://images.unsplash.com/photo-1704656513377-9294b8c54205?q=80&w=1932&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D') no-repeat center center/cover;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    padding-top: 80px;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(26, 26, 46, 0.7);
    opacity: 0.7;
    z-index: 0;
}

.background-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.wave-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    overflow: hidden;
    line-height: 0;
    z-index: 1;
    pointer-events: none;
    opacity: 1; /* Changed to prevent animation conflict */
    /* Removed animation to prevent rise and fall effect */
}

.wave-container svg {
    position: relative;
    display: block;
    width: calc(100% + 1.3px);
    height: 150px;
}

.wave-container .parallax > use {
    animation: move-forever 25s cubic-bezier(.55,.5,.45,.5) infinite;
}

.wave-container .parallax > use:nth-child(1) {
    animation-delay: -2s;
    animation-duration: 7s;
}

.wave-container .parallax > use:nth-child(2) {
    animation-delay: -3s;
    animation-duration: 10s;
}

.wave-container .parallax > use:nth-child(3) {
    animation-delay: -4s;
    animation-duration: 13s;
}

.wave-container .parallax > use:nth-child(4) {
    animation-delay: -5s;
    animation-duration: 20s;
}

@keyframes move-forever {
    0% {
        transform: translate3d(-90px,0,0);
    }
    100% { 
        transform: translate3d(85px,0,0);
    }
}

/* Removed fadeInUpWave animation to prevent rise and fall effect */

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    padding: 0 20px;
}

.hero-content h1 {
    font-size: 3.8rem;
    margin-bottom: 15px;
    animation: fadeInDownModern 1s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
    text-shadow: 0 0 10px rgba(108, 99, 255, 0.5);
    font-weight: 700;
    letter-spacing: -0.5px;
}

.hero-content h2 {
    font-size: 2.2rem;
    margin-bottom: 20px;
    font-weight: 500;
    animation: fadeInDownModern 1.2s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
    background: linear-gradient(90deg, #6c63ff, #00d4ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: 0.5px;
}

.hero-content h2::after {
    content: none;
}

.hero-content .hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 30px;
    animation: fadeInUpModern 1.4s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
    color: #c7b5ff;
    line-height: 1.5;
    text-shadow: 0 0 5px rgba(108, 99, 255, 0.3);
    font-weight: 400;
    letter-spacing: 0.3px;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-bottom: 30px;
    animation: fadeInUpModern 1.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.cta-button {
    display: inline-block;
    padding: 15px 30px;
    background: linear-gradient(90deg, #6c63ff, #00d4ff);
    color: white;
    font-weight: 600;
    border-radius: 50px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    /* Removed pulse animation to prevent button deformation */
    box-shadow: 0 0 15px rgba(108, 99, 255, 0.5);
    min-width: 150px;
    text-align: center;
    letter-spacing: 1px;
}

.cta-button.secondary {
    background: transparent;
    border: 2px solid #6c63ff;
    color: #ffffff;
    box-shadow: 0 0 10px rgba(108, 99, 255, 0.3);
}

.cta-button:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 10px 25px rgba(108, 99, 255, 0.7);
}

.cta-button.secondary:hover {
    background: rgba(108, 99, 255, 0.2);
}

.hero-social-links {
    display: flex;
    justify-content: center;
    gap: 20px;
    animation: fadeInUpModern 1.8s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.hero-social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(108, 99, 255, 0.2);
    color: #e0e0ff;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(108, 99, 255, 0.3);
}

.hero-social-links a:hover {
    background: linear-gradient(90deg, #6c63ff, #00d4ff);
    color: white;
    transform: translateY(-3px) rotate(360deg);
    box-shadow: 0 5px 15px rgba(108, 99, 255, 0.5);
}

/* About Section */
.about {
    padding: 100px 0;
    background-color: #24243e;
    position: relative;
}

.about::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 30%, rgba(108, 99, 255, 0.2) 0%, transparent 50%);
    z-index: 0;
    opacity: 0;
}

.about.visible::before {
    animation: fadeInModern 2s ease-out forwards;
}

.about-content {
    display: flex;
    gap: 50px;
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;
    position: relative;
    z-index: 1;
}

.about-image {
    flex: 1;
    min-width: 300px;
    opacity: 0;
}

.about-image.visible {
    animation: slideInLeftModern 1.2s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.about-image img {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(108, 99, 255, 0.3);
    border: 2px solid #6c63ff;
    transition: transform 0.5s ease, box-shadow 0.5s ease;
}

.about-image img:hover {
    transform: scale(1.03);
    box-shadow: 0 15px 40px rgba(108, 99, 255, 0.5);
}

.about-text {
    flex: 1.5;
    min-width: 300px;
    opacity: 0;
    background-color: rgba(36, 36, 62, 0.7);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(108, 99, 255, 0.3);
    transition: transform 0.5s ease;
}

.about-text.visible {
    animation: slideInRightModern 1.2s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.about-text:hover {
    transform: translateY(-5px);
}

.about-text p {
    margin-bottom: 20px;
    font-size: 1.1rem;
    color: #d0d0ff;
    letter-spacing: 0.3px;
    line-height: 1.7;
}

.download-cv {
    display: inline-block;
    padding: 12px 25px;
    background: linear-gradient(90deg, #6c63ff, #00d4ff);
    color: white;
    border-radius: 50px;
    font-weight: 600;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 0 10px rgba(108, 99, 255, 0.5);
    letter-spacing: 1px;
}

.download-cv:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 5px 20px rgba(108, 99, 255, 0.7);
}

.about-stats {
    display: flex;
    justify-content: space-between;
    margin: 20px 0;
    flex-wrap: wrap;
    gap: 20px;
    opacity: 0;
}

.about-stats.visible {
    animation: fadeInUpModern 1.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

/* Skills Section */
.skills {
    padding: 100px 0;
    background-color: #1e1e36;
    position: relative;
    overflow: hidden;
}

.skills::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 80% 50%, rgba(0, 212, 255, 0.2) 0%, transparent 50%);
    z-index: 0;
    opacity: 0;
}

.skills.visible::before {
    animation: fadeInModern 2s ease-out forwards;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr)); /* Reduced min width for more compact layout */
    gap: 20px; /* Reduced gap for compactness */
    position: relative;
    z-index: 1;
}

.skill-card {
    background-color: rgba(36, 36, 62, 0.8);
    padding: 20px; /* Reduced padding */
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    opacity: 0;
    border: 1px solid rgba(108, 99, 255, 0.2);
    min-height: 180px; /* Reduced min-height for compactness */
}

.skill-card.visible {
    animation: zoomInModern 1s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
    animation-fill-mode: both;
}

.skill-card.visible:nth-child(1) { animation-delay: 0.1s; } /* Reduced animation delays */
.skill-card.visible:nth-child(2) { animation-delay: 0.2s; }
.skill-card.visible:nth-child(3) { animation-delay: 0.3s; }
.skill-card.visible:nth-child(4) { animation-delay: 0.4s; }
.skill-card.visible:nth-child(5) { animation-delay: 0.5s; }
.skill-card.visible:nth-child(6) { animation-delay: 0.6s; }
.skill-card.visible:nth-child(7) { animation-delay: 0.7s; }
.skill-card.visible:nth-child(8) { animation-delay: 0.8s; }
.skill-card.visible:nth-child(9) { animation-delay: 0.9s; }
.skill-card.visible:nth-child(10) { animation-delay: 1.0s; }
.skill-card.visible:nth-child(11) { animation-delay: 1.1s; }
.skill-card.visible:nth-child(12) { animation-delay: 1.2s; }
.skill-card.visible:nth-child(13) { animation-delay: 1.3s; }
.skill-card.visible:nth-child(14) { animation-delay: 1.4s; }
.skill-card.visible:nth-child(15) { animation-delay: 1.5s; }
.skill-card.visible:nth-child(16) { animation-delay: 1.6s; }
.skill-card.visible:nth-child(17) { animation-delay: 1.7s; }
.skill-card.visible:nth-child(18) { animation-delay: 1.8s; }

.skill-card:hover {
    transform: translateY(-5px) scale(1.02); /* Reduced transform for less vertical space */
    box-shadow: 0 10px 25px rgba(108, 99, 255, 0.4);
    border-color: rgba(108, 99, 255, 0.5);
}

.skill-card i {
    font-size: 2.5rem; /* Reduced icon size */
    margin-bottom: 15px;
    background: linear-gradient(90deg, #6c63ff, #00d4ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: iconPulse 3s ease-in-out infinite;
}

.skill-card h3 {
    margin-bottom: 10px; /* Reduced margin */
    font-size: 1.2rem; /* Reduced font size */
    color: #e0e0ff;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.skill-level {
    margin-bottom: 8px; /* Reduced margin */
    font-weight: 600;
    color: #6c63ff;
    letter-spacing: 0.5px;
    font-size: 0.9rem; /* Reduced font size */
}

.skill-bar {
    width: 100%;
    height: 6px; /* Reduced height */
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 3px; /* Adjusted for smaller height */
    overflow: hidden;
    box-shadow: 0 0 5px rgba(108, 99, 255, 0.3);
}

.skill-progress {
    height: 100%;
    background: linear-gradient(90deg, #6c63ff, #00d4ff);
    transition: width 1.5s ease-out;
    box-shadow: 0 0 10px rgba(108, 99, 255, 0.5);
}

/* Skill Description */
.skill-description {
    margin-top: 10px; /* Reduced margin */
    font-size: 0.8rem; /* Reduced font size */
    color: #c0c0ff;
    line-height: 1.3; /* Adjusted line height */
    letter-spacing: 0.3px;
}

/* Projects Section */
.projects {
    padding: 100px 0;
    background-color: #24243e;
    position: relative;
}

.projects::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(108, 99, 255, 0.1) 0%, rgba(0, 212, 255, 0.1) 100%);
    z-index: 0;
    opacity: 0;
}

.projects.visible::before {
    animation: fadeInModern 2s ease-out forwards;
}

.project-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    position: relative;
    z-index: 1;
}

.project-card {
    background-color: rgba(36, 36, 62, 0.8);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    opacity: 0;
    border: 1px solid rgba(108, 99, 255, 0.2);
}

.project-card.visible {
    animation: slideUpModern 1s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
    animation-fill-mode: both;
}

.project-card.visible:nth-child(1) { animation-delay: 0.2s; }
.project-card.visible:nth-child(2) { animation-delay: 0.4s; }
.project-card.visible:nth-child(3) { animation-delay: 0.6s; }

.project-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 15px 30px rgba(108, 99, 255, 0.4);
    border-color: rgba(108, 99, 255, 0.5);
}

.project-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    position: relative;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease, filter 0.5s ease;
}

.project-card:hover .project-image img {
    transform: scale(1.1);
    filter: brightness(0.8);
}

.project-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top, rgba(36, 36, 62, 0.8) 0%, transparent 50%);
}

.project-info {
    padding: 20px;
}

.project-info h3 {
    margin-bottom: 10px;
    color: #e0e0ff;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.project-info p {
    margin-bottom: 20px;
    color: #c0c0ff;
    font-size: 0.95rem;
    letter-spacing: 0.3px;
    line-height: 1.6;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
}

.project-tech span {
    background-color: rgba(108, 99, 255, 0.3);
    color: #e0e0ff;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    transition: background-color 0.3s ease;
    letter-spacing: 0.5px;
}

.project-tech span:hover {
    background-color: rgba(108, 99, 255, 0.5);
}

.project-links {
    display: flex;
    gap: 15px;
}

.link-btn {
    flex: 1;
    text-align: center;
    padding: 8px 0;
    border: 2px solid #6c63ff;
    border-radius: 50px;
    color: #6c63ff;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.link-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.link-btn:hover::before {
    left: 100%;
}

.link-btn:hover {
    background-color: rgba(108, 99, 255, 0.2);
    color: #ffffff;
    box-shadow: 0 0 10px rgba(108, 99, 255, 0.5);
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background-color: #1e1e36;
    position: relative;
}

.contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 30% 70%, rgba(108, 99, 255, 0.15) 0%, transparent 50%);
    z-index: 0;
    opacity: 0;
}

.contact.visible::before {
    animation: fadeInModern 2s ease-out forwards;
}

.contact-wrapper {
    display: flex;
    gap: 50px;
    flex-wrap: wrap;
    justify-content: center;
    position: relative;
    z-index: 1;
}

.contact-info {
    flex: 1;
    min-width: 300px;
    opacity: 0;
}

.contact-info.visible {
    animation: slideInLeftModern 1.2s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.contact-item {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
    align-items: flex-start;
    background-color: rgba(36, 36, 62, 0.6);
    padding: 20px;
    border-radius: 10px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(108, 99, 255, 0.2);
}

.contact-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(108, 99, 255, 0.3);
}

.contact-item i {
    font-size: 1.8rem;
    color: #6c63ff;
    background: rgba(108, 99, 255, 0.2);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.contact-item:hover i {
    transform: scale(1.1);
}

.contact-item h3 {
    font-size: 1.2rem;
    margin-bottom: 5px;
    color: #e0e0ff;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.contact-item p {
    color: #c0c0ff;
    letter-spacing: 0.3px;
}

.social-links {
    display: flex;
    gap: 20px;
    margin-top: 30px;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(108, 99, 255, 0.2);
    color: #e0e0ff;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(108, 99, 255, 0.3);
}

.social-links a:hover {
    background: linear-gradient(90deg, #6c63ff, #00d4ff);
    color: white;
    transform: translateY(-3px) rotate(360deg);
    box-shadow: 0 5px 15px rgba(108, 99, 255, 0.5);
}

.contact-form {
    flex: 1.5;
    min-width: 300px;
    background-color: rgba(36, 36, 62, 0.8);
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    opacity: 0;
    border: 1px solid rgba(108, 99, 255, 0.3);
}

.contact-form.visible {
    animation: slideInRightModern 1.2s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #e0e0ff;
    transition: color 0.3s ease;
    letter-spacing: 0.5px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid rgba(108, 99, 255, 0.3);
    border-radius: 5px;
    font-family: inherit;
    font-size: 1rem;
    background-color: rgba(26, 26, 46, 0.8);
    color: #ffffff;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #6c63ff;
    box-shadow: 0 0 10px rgba(108, 99, 255, 0.3);
}

.form-group input:focus + label,
.form-group textarea:focus + label {
    color: #6c63ff;
}

.submit-btn {
    display: block;
    width: 100%;
    padding: 15px;
    background: linear-gradient(90deg, #6c63ff, #00d4ff);
    color: white;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
    letter-spacing: 1px;
}

.submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.submit-btn:hover::before {
    left: 100%;
}

.submit-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 5px 15px rgba(108, 99, 255, 0.5);
}

/* Footer */
.footer {
    background-color: #1a1a2e;
    color: #c0c0ff;
    padding: 30px 0;
    border-top: 1px solid rgba(108, 99, 255, 0.2);
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 50% 30%, rgba(108, 99, 255, 0.1) 0%, transparent 50%);
    z-index: 0;
    opacity: 0;
}

.footer.visible::before {
    animation: fadeInModern 2s ease-out forwards;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
    opacity: 0;
}

.footer.visible .footer-content {
    animation: fadeInUpModern 1.2s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.footer-logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: #7c6eff;
    margin-bottom: 0;
    position: relative;
    display: inline-block;
    letter-spacing: 1px;
}

.footer-logo h3 {
    margin: 0;
}

.footer-logo h3::before {
    content: '';
    position: absolute;
    left: -10px;
    width: 5px;
    height: 20px;
    background: linear-gradient(90deg, #7c6eff, #00ccff);
    border-radius: 2px;
}

.footer-center {
    text-align: center;
    opacity: 0;
}

.footer.visible .footer-center {
    animation: fadeInModern 1.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
    animation-delay: 0.3s;
}

.footer-center p {
    margin: 5px 0;
    letter-spacing: 0.5px;
}

.footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    justify-content: flex-end;
    align-items: center;
}

.footer-col h4 {
    font-size: 1rem;
    margin-bottom: 10px;
    color: #e0e0ff;
    position: relative;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.footer-col h4::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 25px;
    height: 2px;
    background: linear-gradient(90deg, #6c63ff, #00d4ff);
}

.footer-col ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-col ul li {
    margin-bottom: 8px;
}

.footer-col ul li a {
    color: #c0c0ff;
    font-size: 0.85rem;
    transition: color 0.3s ease;
    letter-spacing: 0.5px;
}

.footer-col ul li a:hover {
    color: #6c63ff;
}

.footer-col .social-links {
    display: flex;
    gap: 10px;
    margin-top: 5px;
}

.footer-col .social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: rgba(108, 99, 255, 0.2);
    color: #e0e0ff;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(108, 99, 255, 0.3);
}

.footer-col .social-links a:hover {
    background: linear-gradient(90deg, #6c63ff, #00d4ff);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(108, 99, 255, 0.5);
}

.footer-bottom {
    display: none;
}

@media (max-width: 768px) {
    .footer-content {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .footer-logo {
        margin-bottom: 15px;
    }

    .footer-links {
        flex-direction: column;
        gap: 20px;
        width: 100%;
        align-items: center;
        justify-content: center;
    }

    .footer-col h4::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .footer-center {
        margin-top: 20px;
    }
}

/* Light Mode Styles for Footer */
body.light-mode .footer {
    background-color: #e5e5e5;
    color: #555555;
    border-top: 1px solid rgba(74, 58, 255, 0.1);
}

body.light-mode .footer-logo h3 {
    color: #4a3aff;
}

body.light-mode .footer-logo h3::before {
    background: linear-gradient(90deg, #4a3aff, #00aaff);
}

body.light-mode .footer-col h4 {
    color: #333333;
}

body.light-mode .footer-col h4::after {
    background: linear-gradient(90deg, #4a3aff, #00aaff);
}

body.light-mode .footer-col ul li a {
    color: #555555;
}

body.light-mode .footer-col ul li a:hover {
    color: #4a3aff;
}

body.light-mode .footer-col .social-links a {
    background-color: rgba(74, 58, 255, 0.2);
    color: #333333;
    border: 1px solid rgba(74, 58, 255, 0.3);
}

body.light-mode .footer-col .social-links a:hover {
    background: linear-gradient(90deg, #4a3aff, #00aaff);
    color: white;
    box-shadow: 0 5px 15px rgba(74, 58, 255, 0.5);
}

body.light-mode .footer-bottom {
    border-top: 1px solid rgba(74, 58, 255, 0.1);
}

/* Animations */
@keyframes fadeInDownModern {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes fadeInUpModern {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes fadeInLeftModern {
    from {
        opacity: 0;
        transform: translateX(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes fadeInRightModern {
    from {
        opacity: 0;
        transform: translateX(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes slideInLeftModern {
    from {
        opacity: 0;
        transform: translateX(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes slideInRightModern {
    from {
        opacity: 0;
        transform: translateX(50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes slideUpModern {
    from {
        opacity: 0;
        transform: translateY(40px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes zoomInModern {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes scaleYModern {
    from {
        opacity: 0;
        transform: scaleY(0);
    }
    to {
        opacity: 1;
        transform: scaleY(1);
    }
}

@keyframes fadeInModern {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        display: none;
        position: absolute;
        top: 60px;
        right: 0;
        background-color: #048ac9;
        flex-direction: column;
        width: 100%;
        padding: 15px 0;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }

    .nav-links.active {
        display: flex;
        transform: translateX(0);
    }

    .nav-links li a {
        font-size: 0.9rem;
        padding: 10px 20px;
    }

    .navbar {
        padding: 15px 20px;
        background-color: #048ac9;
    }

    .navbar.scrolled {
        background-color: #048ac9;
    }

    .logo {
        font-size: 1.4rem;
        color: white;
    }

    .logo:hover {
        color: #f0f0f0;
    }

    .logo::before {
        background: linear-gradient(90deg, white, #f0f0f0);
    }

    .hamburger {
        display: flex;
    }

    .hero {
        background: url('https://images.unsplash.com/photo-1604308343044-71bb4cc521b5?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D') no-repeat center center/cover;
    }

    .hero-content h1 {
        font-size: 1.8rem;
    }

    .hero-content h2 {
        font-size: 1.2rem;
    }

    .hero-subtitle {
        font-size: 0.9rem;
        margin-bottom: 20px;
    }

    .hero-buttons {
        gap: 10px;
    }

    .cta-button {
        padding: 10px 20px;
        min-width: 120px;
        font-size: 0.9rem;
    }

    .hero-social-links a {
        width: 30px;
        height: 30px;
        font-size: 1rem;
    }

    .about-content,
    .contact-wrapper {
        flex-direction: column;
    }

    .contact-info,
    .contact-form {
        width: 100%;
    }

    .about-text {
        padding: 20px;
    }
}

/* Experience Section */
.experience {
    padding: 100px 0;
    background-color: #1e1e36;
    position: relative;
}

.experience::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 70% 30%, rgba(108, 99, 255, 0.15) 0%, transparent 50%);
    z-index: 0;
    opacity: 0;
}

.experience.visible::before {
    animation: fadeInModern 2s ease-out forwards;
}

.timeline {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    z-index: 1;
}

.timeline::after {
    content: '';
    position: absolute;
    width: 6px;
    background: linear-gradient(to bottom, #6c63ff, #00d4ff);
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -3px;
    transform: translateX(-50%);
    opacity: 0;
}

.timeline.visible::after {
    animation: scaleYModern 1.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
    animation-delay: 0.5s;
}

.timeline-item {
    padding: 10px 40px;
    position: relative;
    width: 50%;
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    transition: opacity 0.8s cubic-bezier(0.34, 1.56, 0.64, 1), transform 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.timeline-item.visible {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.timeline-item::after {
    content: '';
    position: absolute;
    width: 26px;
    height: 26px;
    right: -13px;
    background-color: #1e1e36;
    border: 4px solid #6c63ff;
    top: 15px;
    border-radius: 50%;
    z-index: 1;
    box-shadow: 0 0 10px rgba(108, 99, 255, 0.5);
}

.left {
    left: 0;
}

.right {
    left: 50%;
}

.right::after {
    left: -13px;
    right: auto;
}

.timeline-content {
    padding: 20px;
    background-color: rgba(36, 36, 62, 0.8);
    position: relative;
    border-radius: 6px;
    border: 1px solid rgba(108, 99, 255, 0.3);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.timeline-content h3 {
    margin-bottom: 5px;
    color: #e0e0ff;
    font-size: 1.4rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.timeline-content h4 {
    margin-bottom: 10px;
    color: #c0c0ff;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.timeline-content .date {
    display: inline-block;
    padding: 5px 10px;
    background: linear-gradient(90deg, #6c63ff, #00d4ff);
    color: white;
    border-radius: 20px;
    font-size: 0.8rem;
    margin-bottom: 15px;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.timeline-content p {
    color: #d0d0ff;
    line-height: 1.6;
    letter-spacing: 0.3px;
}

@media (max-width: 768px) {
    .timeline::after {
        left: 31px;
        transform: none;
    }

    .timeline-item {
        width: 100%;
        padding-left: 70px;
        padding-right: 20px;
    }

    .timeline-item::after {
        left: 15px;
        right: auto;
    }

    .right {
        left: 0%;
    }
}

/* Updated Project Styles */
.project-stats {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    color: #c0c0ff;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

.project-stats i {
    margin-right: 5px;
    color: #6c63ff;
}

.view-more {
    text-align: center;
    margin-top: 40px;
    opacity: 0;
}

.view-more.visible {
    animation: fadeInModern 1.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.view-more-btn {
    display: inline-block;
    padding: 12px 25px;
    background: linear-gradient(90deg, #6c63ff, #00d4ff);
    color: white;
    border-radius: 50px;
    font-weight: 600;
    transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
    box-shadow: 0 0 15px rgba(108, 99, 255, 0.6);
    letter-spacing: 1px;
    border: none;
    cursor: pointer; /* Ensure pointer cursor on hover */
}

.view-more-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 5px 25px rgba(108, 99, 255, 0.8);
}

/* About Stats */
.about-stats {
    display: flex;
    justify-content: space-between;
    margin: 20px 0;
    flex-wrap: wrap;
    gap: 20px;
}

.stat {
    text-align: center;
    min-width: 100px;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(90deg, #6c63ff, #00d4ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 5px;
    letter-spacing: 1px;
}

.stat-text {
    font-size: 0.9rem;
    color: #c0c0ff;
    letter-spacing: 0.5px;
}

/* Skill Description */
.skill-description {
    margin-top: 10px; /* Reduced margin */
    font-size: 0.8rem; /* Reduced font size */
    color: #c0c0ff;
    line-height: 1.3; /* Adjusted line height */
    letter-spacing: 0.3px;
}

/* Remove particle styles */
.particle-container {
    display: none;
}

.particle {
    display: none;
}

/* Light Mode Styles */
body.light-mode {
    color: #333333;
    background-color: #f5f5f5;
}

body.light-mode a {
    color: #4a3aff;
}

body.light-mode h2 {
    color: #4a3aff;
}

body.light-mode h2::after {
    background: linear-gradient(90deg, #4a3aff, #00aaff);
}

body.light-mode .navbar {
    background-color: rgba(4, 138, 201, 0.6);
    box-shadow: 0 2px 10px rgba(4, 138, 201, 0.4);
}

body.light-mode .navbar.scrolled {
    background-color: rgba(4, 138, 201, 0.65);
    box-shadow: 0 5px 20px rgba(4, 138, 201, 0.5);
}

body.light-mode .logo {
    color: white;
}

body.light-mode .logo:hover {
    color: #f0f0f0;
}

body.light-mode .logo::before {
    background: linear-gradient(90deg, white, #f0f0f0);
}

body.light-mode .nav-links li a {
    color: #ffffff;
}

body.light-mode .nav-links li a:hover {
    color: #00aaff;
}

body.light-mode .nav-links li a::before {
    background: linear-gradient(90deg, #4a3aff, #00aaff);
}

body.light-mode .profile-icon {
    background-color: rgba(13, 100, 117, 0.4);
    color: #ffffff;
    border: 1px solid rgba(13, 100, 117, 0.6);
}

body.light-mode .profile-icon:hover {
    background: linear-gradient(90deg, #4a3aff, #00aaff);
    color: white;
    box-shadow: 0 0 10px rgba(13, 100, 117, 0.6);
}

body.light-mode .profile-dropdown {
    background-color: rgba(13, 13, 23, 0.8);
    border: 1px solid rgba(13, 100, 117, 0.5);
    box-shadow: 0 10px 20px rgba(13, 100, 117, 0.4);
}

body.light-mode .profile-dropdown a {
    color: #ffffff;
    border-bottom: 1px solid rgba(13, 100, 117, 0.4);
}

body.light-mode .profile-dropdown a:hover {
    background-color: rgba(13, 100, 117, 0.3);
    color: #00aaff;
}

body.light-mode .theme-toggle {
    background-color: rgba(13, 100, 117, 0.4);
    color: #ffffff;
    border: 1px solid rgba(13, 100, 117, 0.6);
}

body.light-mode .theme-toggle:hover {
    background: linear-gradient(90deg, #4a3aff, #00aaff);
    color: white;
    box-shadow: 0 0 10px rgba(13, 100, 117, 0.6);
}

body.light-mode .theme-dropdown {
    background-color: rgba(13, 13, 23, 0.8);
    border: 1px solid rgba(13, 100, 117, 0.5);
    box-shadow: 0 10px 20px rgba(13, 100, 117, 0.4);
}

body.light-mode .theme-dropdown a {
    color: #ffffff;
    border-bottom: 1px solid rgba(13, 100, 117, 0.4);
}

body.light-mode .theme-dropdown a:hover {
    background-color: rgba(13, 100, 117, 0.3);
    color: #00aaff;
}

body.light-mode .hamburger span {
    background-color: #ffffff;
}

body.light-mode .hamburger:hover span {
    background-color: #00aaff;
}

body.light-mode .hamburger.active span:nth-child(1) {
    background-color: #00aaff;
}

body.light-mode .hamburger.active span:nth-child(3) {
    background-color: #00aaff;
}

body.light-mode .nav-links.active {
    background-color: #048ac9;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

body.light-mode .hero::before {
    background: transparent;
}

body.light-mode .hero-content h1 {
    color: #ffffff;
    text-shadow: 0 0 10px rgba(74, 58, 255, 0.8), 0 0 20px rgba(0, 0, 0, 0.5);
}

body.light-mode .hero-content h2 {
    font-weight: 400;
    background: linear-gradient(90deg, #ffffff, #e0e0e0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 10px rgba(74, 58, 255, 0.5), 0 0 15px rgba(0, 0, 0, 0.3);
}

body.light-mode .hero-content .hero-subtitle {
    color: #f0f0f0;
    text-shadow: 0 0 5px rgba(74, 58, 255, 0.5), 0 0 10px rgba(0, 0, 0, 0.4);
}

body.light-mode .cta-button {
    background: linear-gradient(90deg, #4a3aff, #00aaff);
    color: white;
    box-shadow: 0 0 15px rgba(74, 58, 255, 0.7), 0 0 10px rgba(0, 0, 0, 0.3);
}

body.light-mode .cta-button.secondary {
    background: transparent;
    border: 2px solid #ffffff;
    color: #ffffff;
    box-shadow: 0 0 10px rgba(74, 58, 255, 0.5), 0 0 5px rgba(0, 0, 0, 0.3);
}

body.light-mode .cta-button:hover {
    box-shadow: 0 10px 25px rgba(74, 58, 255, 0.9), 0 0 15px rgba(0, 0, 0, 0.4);
}

body.light-mode .cta-button.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
}

body.light-mode .hero-social-links a {
    background-color: rgba(74, 58, 255, 0.2);
    color: #333333;
    border: 1px solid rgba(74, 58, 255, 0.3);
}

body.light-mode .hero-social-links a:hover {
    background: linear-gradient(90deg, #4a3aff, #00aaff);
    color: white;
    box-shadow: 0 5px 15px rgba(74, 58, 255, 0.5);
}

body.light-mode .about {
    background-color: #ffffff;
}

body.light-mode .about::before {
    background: radial-gradient(circle at 20% 30%, rgba(74, 58, 255, 0.1) 0%, transparent 50%);
}

body.light-mode .about-text {
    background-color: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(74, 58, 255, 0.2);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

body.light-mode .about-text p {
    color: #555555;
}

body.light-mode .about-stats .stat-number {
    background: linear-gradient(90deg, #4a3aff, #00aaff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

body.light-mode .about-stats .stat-text {
    color: #666666;
}

body.light-mode .download-cv {
    background: linear-gradient(90deg, #4a3aff, #00aaff);
    box-shadow: 0 0 10px rgba(74, 58, 255, 0.5);
    color: white;
}

body.light-mode .download-cv:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 5px 20px rgba(74, 58, 255, 0.7);
}

body.light-mode .skills {
    background-color: #f0f0f5;
}

body.light-mode .skills::before {
    background: radial-gradient(circle at 80% 50%, rgba(0, 170, 255, 0.1) 0%, transparent 50%);
}

body.light-mode .skill-card {
    background-color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(74, 58, 255, 0.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

body.light-mode .skill-card:hover {
    box-shadow: 0 10px 25px rgba(74, 58, 255, 0.3);
    border-color: rgba(74, 58, 255, 0.4);
}

body.light-mode .skill-card i {
    background: linear-gradient(90deg, #4a3aff, #00aaff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

body.light-mode .skill-card h3 {
    color: #333333;
}

body.light-mode .skill-level {
    color: #4a3aff;
}

body.light-mode .skill-bar {
    background-color: rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 5px rgba(74, 58, 255, 0.2);
}

body.light-mode .skill-progress {
    background: linear-gradient(90deg, #4a3aff, #00aaff);
    box-shadow: 0 0 10px rgba(74, 58, 255, 0.4);
}

body.light-mode .skill-description {
    color: #666666;
}

body.light-mode .projects {
    background-color: #ffffff;
}

body.light-mode .projects::before {
    background: linear-gradient(45deg, rgba(74, 58, 255, 0.05) 0%, rgba(0, 170, 255, 0.05) 100%);
}

body.light-mode .project-card {
    background-color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(74, 58, 255, 0.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

body.light-mode .project-card:hover {
    box-shadow: 0 15px 30px rgba(74, 58, 255, 0.3);
    border-color: rgba(74, 58, 255, 0.4);
}

body.light-mode .project-info h3 {
    color: #333333;
}

body.light-mode .project-info p {
    color: #555555;
}

body.light-mode .project-tech span {
    background-color: rgba(74, 58, 255, 0.2);
    color: #333333;
}

body.light-mode .project-tech span:hover {
    background-color: rgba(74, 58, 255, 0.3);
}

body.light-mode .link-btn {
    border: 2px solid #4a3aff;
    color: #4a3aff;
}

body.light-mode .link-btn:hover {
    background-color: rgba(74, 58, 255, 0.1);
    color: #333333;
    box-shadow: 0 0 10px rgba(74, 58, 255, 0.4);
}

body.light-mode .project-stats {
    color: #555555;
}

body.light-mode .project-stats i {
    color: #4a3aff;
}

body.light-mode .view-more-btn {
    background: linear-gradient(90deg, #4a3aff, #00aaff);
    color: white;
    box-shadow: 0 0 15px rgba(74, 58, 255, 0.6);
}

body.light-mode .view-more-btn:hover {
    box-shadow: 0 5px 25px rgba(74, 58, 255, 0.8);
}

body.light-mode .experience {
    background-color: #f0f0f5;
}

body.light-mode .experience::before {
    background: radial-gradient(circle at 70% 30%, rgba(74, 58, 255, 0.1) 0%, transparent 50%);
}

body.light-mode .timeline::after {
    background: linear-gradient(to bottom, #4a3aff, #00aaff);
}

body.light-mode .timeline-item::after {
    background-color: #f0f0f5;
    border: 4px solid #4a3aff;
    box-shadow: 0 0 10px rgba(74, 58, 255, 0.4);
}

body.light-mode .timeline-content {
    background-color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(74, 58, 255, 0.2);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

body.light-mode .timeline-content h3 {
    color: #333333;
}

body.light-mode .timeline-content h4 {
    color: #555555;
}

body.light-mode .timeline-content .date {
    background: linear-gradient(90deg, #4a3aff, #00aaff);
}

body.light-mode .timeline-content p {
    color: #666666;
}

body.light-mode .contact {
    background-color: #f0f0f5;
}

body.light-mode .contact::before {
    background: radial-gradient(circle at 30% 70%, rgba(74, 58, 255, 0.1) 0%, transparent 50%);
}

body.light-mode .contact-item {
    background-color: rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(74, 58, 255, 0.1);
}

body.light-mode .contact-item:hover {
    box-shadow: 0 5px 15px rgba(74, 58, 255, 0.2);
}

body.light-mode .contact-item i {
    color: #4a3aff;
    background: rgba(74, 58, 255, 0.1);
}

body.light-mode .contact-item h3 {
    color: #333333;
}

body.light-mode .contact-item p {
    color: #555555;
}

body.light-mode .social-links a {
    background-color: rgba(74, 58, 255, 0.2);
    color: #333333;
    border: 1px solid rgba(74, 58, 255, 0.3);
}

body.light-mode .social-links a:hover {
    background: linear-gradient(90deg, #4a3aff, #00aaff);
    color: white;
    box-shadow: 0 5px 15px rgba(74, 58, 255, 0.5);
}

body.light-mode .contact-form {
    background-color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(74, 58, 255, 0.2);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

body.light-mode .form-group label {
    color: #333333;
}

body.light-mode .form-group input,
body.light-mode .form-group textarea {
    background-color: rgba(245, 245, 245, 0.8);
    color: #333333;
    border: 1px solid rgba(74, 58, 255, 0.2);
}

body.light-mode .form-group input:focus,
body.light-mode .form-group textarea:focus {
    border-color: #4a3aff;
    box-shadow: 0 0 10px rgba(74, 58, 255, 0.3);
}

body.light-mode .form-group input:focus + label,
body.light-mode .form-group textarea:focus + label {
    color: #4a3aff;
}

body.light-mode .submit-btn {
    background: linear-gradient(90deg, #4a3aff, #00aaff);
    box-shadow: 0 0 10px rgba(74, 58, 255, 0.5);
}

body.light-mode .submit-btn:hover {
    box-shadow: 0 5px 15px rgba(74, 58, 255, 0.7);
}

body.light-mode .footer {
    background-color: #e5e5e5;
    color: #555555;
    border-top: 1px solid rgba(74, 58, 255, 0.1);
}

body.light-mode .nav-links.active {
    background-color: #048ac9;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* End of Light Mode Styles */

/* Preloader */
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #1a1a2e;
    display: flex;
    flex-direction: column; /* Stack elements vertically */
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 1s;
    visibility: visible; /* Force visibility */
    opacity: 1; /* Force opacity */
}

.dot-pulse-container {
    display: flex;
    gap: 10px;
    margin-bottom: 15px; /* Space between dots and text */
    visibility: visible; /* Force visibility */
}

.dot-pulse {
    width: 15px;
    height: 15px;
    background: linear-gradient(90deg, #6c63ff, #00d4ff);
    border-radius: 50%;
    animation: dotPulse 1.5s ease-in-out infinite;
    display: block; /* Ensure dots are rendered */
    visibility: visible; /* Force visibility */
    opacity: 1; /* Force opacity */
}

.dot-pulse:nth-child(2) {
    animation-delay: 0.2s;
}

.dot-pulse:nth-child(3) {
    animation-delay: 0.4s;
}

.loading-text {
    font-size: 1.2rem;
    font-weight: 600;
    color: #e0e0ff;
    letter-spacing: 2px;
    text-transform: uppercase;
    display: flex;
    align-items: center;
    background: linear-gradient(90deg, #6c63ff, #00d4ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: textGlow 2s ease-in-out infinite alternate;
    visibility: visible; /* Force visibility */
}

.dot-animation {
    display: inline-block;
    animation: dotTextPulse 1.5s ease-in-out infinite;
    margin-left: 5px;
    font-size: 1.2rem;
    visibility: visible; /* Force visibility */
}

@keyframes dotPulse {
    0%, 100% {
        transform: scale(0.5);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
}

@keyframes dotTextPulse {
    0%, 100% {
        opacity: 0.3;
    }
    50% {
        opacity: 1;
    }
}

@keyframes textGlow {
    from {
        text-shadow: 0 0 5px rgba(108, 99, 255, 0.5);
    }
    to {
        text-shadow: 0 0 15px rgba(108, 99, 255, 0.8), 0 0 25px rgba(0, 212, 255, 0.6);
    }
}

/* Light Mode Styles for New Functionalities */
body.light-mode .preloader {
    background: #e5e5e5;
    visibility: visible; /* Force visibility */
}

body.light-mode .dot-pulse {
    background: linear-gradient(90deg, #4a3aff, #00aaff);
    visibility: visible; /* Force visibility */
}

body.light-mode .loading-text {
    color: transparent; /* Needed for gradient to show */
    background: linear-gradient(90deg, #4a3aff, #00aaff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: textGlowLight 2s ease-in-out infinite alternate;
    visibility: visible; /* Force visibility */
}

@keyframes textGlowLight {
    from {
        text-shadow: 0 0 5px rgba(74, 58, 255, 0.5);
    }
    to {
        text-shadow: 0 0 15px rgba(74, 58, 255, 0.8), 0 0 25px rgba(0, 170, 255, 0.6);
    }
}

body.light-mode .filter-btn {
    background: rgba(74, 58, 255, 0.2);
    color: #333333;
    border: 1px solid rgba(74, 58, 255, 0.3);
    cursor: pointer; /* Ensure pointer cursor in light mode */
}

body.light-mode .filter-btn:hover {
    background: rgba(74, 58, 255, 0.4);
    box-shadow: 0 0 10px rgba(74, 58, 255, 0.4);
    color: #ffffff;
}

body.light-mode .filter-btn.active {
    background: linear-gradient(90deg, #4a3aff, #00aaff);
    color: white;
    box-shadow: 0 0 15px rgba(74, 58, 255, 0.7);
    transform: scale(1.02);
}

body.light-mode .achievements {
    background-color: #f0f0f5;
}

body.light-mode .achievements::before {
    background: radial-gradient(circle at 50% 50%, rgba(74, 58, 255, 0.1) 0%, transparent 50%);
}

body.light-mode .achievement-card {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(74, 58, 255, 0.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

body.light-mode .achievement-card:hover {
    box-shadow: 0 10px 25px rgba(74, 58, 255, 0.3);
}

body.light-mode .achievement-card img {
    border: 2px solid rgba(74, 58, 255, 0.2);
}

body.light-mode .achievement-card h3 {
    color: #333333;
}

body.light-mode .achievement-card p {
    color: #555555;
}

body.light-mode .swiper-pagination-bullet {
    background: rgba(74, 58, 255, 0.4);
    opacity: 0.7;
}

body.light-mode .swiper-pagination-bullet-active {
    background: #00aaff;
    opacity: 1;
    transform: scale(1.2);
}

/* Project Filter Buttons */
.filter-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.filter-btn {
    background: rgba(108, 99, 255, 0.3);
    color: #e0e0ff;
    border: 1px solid rgba(108, 99, 255, 0.5);
    padding: 10px 20px;
    border-radius: 50px;
    cursor: pointer; /* Ensure pointer cursor on hover */
    font-weight: 500;
    transition: background 0.3s, transform 0.3s, box-shadow 0.3s, color 0.3s;
    letter-spacing: 0.5px;
    position: relative;
    z-index: 1; /* Ensure buttons are above other elements */
}

.filter-btn:hover {
    background: rgba(108, 99, 255, 0.6);
    transform: scale(1.05);
    box-shadow: 0 0 10px rgba(108, 99, 255, 0.5);
    color: white; /* Ensure text color changes on hover */
}

.filter-btn.active {
    background: linear-gradient(90deg, #6c63ff, #00d4ff);
    color: white;
    box-shadow: 0 0 15px rgba(108, 99, 255, 0.7);
    transform: scale(1.02); /* Slight scale to emphasize active state */
    z-index: 2; /* Higher z-index for active button */
}

/* Achievements Section */
.achievements {
    padding: 100px 0;
    background-color: #1e1e36;
    position: relative;
}

.achievements::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 50% 50%, rgba(108, 99, 255, 0.15) 0%, transparent 50%);
    z-index: 0;
    opacity: 0;
}

.achievements.visible::before {
    animation: fadeInModern 2s ease-out forwards;
}

.achievements-carousel {
    max-width: 900px;
    margin: 0 auto;
}

.achievement-card {
    background: rgba(36, 36, 62, 0.8);
    border-radius: 10px;
    padding: 25px;
    text-align: center;
    transition: transform 0.3s, box-shadow 0.3s;
    border: 1px solid rgba(108, 99, 255, 0.2);
    margin: 10px;
    opacity: 0;
    transform: translateY(30px) scale(0.9);
}

.achievement-card.visible {
    animation: slideUpAndGlow 1s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
    animation-fill-mode: both;
}

.achievement-card.visible:nth-child(1) { animation-delay: 0.2s; }
.achievement-card.visible:nth-child(2) { animation-delay: 0.4s; }
.achievement-card.visible:nth-child(3) { animation-delay: 0.6s; }

.achievement-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 25px rgba(108, 99, 255, 0.4);
}

.achievement-card img {
    max-width: 120px;
    margin-bottom: 20px;
    border-radius: 10px;
    border: 2px solid rgba(108, 99, 255, 0.3);
    transition: transform 0.5s ease;
}

.achievement-card:hover img {
    transform: scale(1.1) rotate(5deg);
}

.achievement-card h3 {
    color: #e0e0ff;
    margin-bottom: 10px;
    font-size: 1.4rem;
    font-weight: 600;
}

.achievement-card p {
    color: #c0c0ff;
    font-size: 0.95rem;
    line-height: 1.5;
}

.swiper-pagination-bullet {
    background: rgba(108, 99, 255, 0.5);
    opacity: 0.7;
}

.swiper-pagination-bullet-active {
    background: #00d4ff;
    opacity: 1;
    transform: scale(1.2);
}

/* Responsive adjustments for filter buttons */
@media (max-width: 768px) {
    .filter-buttons {
        gap: 10px;
    }
    
    .filter-btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }
}

/* Responsive adjustments for achievements section */
@media (max-width: 768px) {
    .achievements-carousel {
        max-width: 100%;
        padding: 0 20px; /* Add padding to prevent edge clipping */
    }
    
    .achievement-card {
        padding: 20px;
        min-height: 250px; /* Adjust minimum height for mobile */
        margin: 10px 0; /* Stack vertically with top/bottom margin */
        width: 100%; /* Full width to stack one per row */
    }
    
    .achievement-card h3 {
        font-size: 1.2rem; /* Smaller font on mobile */
    }
    
    .achievement-card p {
        font-size: 0.85rem; /* Smaller font on mobile */
    }
    
    .achievement-card img {
        max-width: 100px; /* Smaller image on mobile */
    }
    
    .swiper-pagination {
        padding-bottom: 10px; /* Ensure pagination is visible */
    }
    
    .swiper-container {
        min-height: 300px; /* Adjust for mobile */
    }
    
    .swiper-wrapper {
        min-height: 250px; /* Adjust for mobile */
        flex-direction: column; /* Stack cards vertically in fallback */
        align-items: center; /* Center align cards */
    }
    
    .swiper-slide {
        min-height: 250px; /* Adjust for mobile */
        width: 100%; /* Full width to ensure one per row */
        margin-bottom: 20px; /* Space between stacked cards */
    }
}



