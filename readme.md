# Full-Stack Developer Portfolio

A modern, responsive portfolio website for a Full-Stack & Web Developer built with HTML, CSS, and JavaScript.

## Features
- **Responsive Design**: Works seamlessly on desktop and mobile devices.
- **Modern Animations**: Smooth transitions and effects for a dynamic user experience.
- **Sections**: Includes Home, About, Skills, Projects, and Contact sections.
- **Interactive Elements**: Smooth scrolling, skill bar animations, and a functional contact form (demo).

## Getting Started
1. **Clone or Download**: Get the repository or download the files.
2. **Open `index.html`**: Simply open the `index.html` file in a web browser to view the portfolio.

## Customization
- **Personal Information**: Edit the content in `index.html` to reflect your name, skills, projects, and contact information.
- **Images**: Replace `profile.jpg`, `project1.jpg`, `project2.jpg`, and `project3.jpg` with your own images.
- **Styling**: Modify `styles.css` to change colors, fonts, or layout to match your personal brand.
- **Functionality**: Extend `script.js` to add more interactive features as needed.

## Structure
- `index.html` - Main HTML file with the portfolio structure.
- `styles.css` - CSS file for styling and animations.
- `script.js` - JavaScript file for interactivity.

## License
This project is open for personal use and modification. Feel free to customize it to showcase your work! 